from ucimlrepo import fetch_ucirepo
import pandas as pd
import numpy as np
import os

# fetch dataset
breast_cancer_wisconsin_diagnostic = fetch_ucirepo(id=17)

# data (as pandas dataframes)
X = breast_cancer_wisconsin_diagnostic.data.features
y = breast_cancer_wisconsin_diagnostic.data.targets

# metadata
print(breast_cancer_wisconsin_diagnostic.metadata)

# variable information
print(breast_cancer_wisconsin_diagnostic.variables)

# 合并特征和标签
data_combined = pd.concat([X, y], axis=1)

# 检查数据信息
print("\n数据集形状:", data_combined.shape)
print(f"\n样本数量: {data_combined.shape[0]}")
print(f"特征数量: {data_combined.shape[1]}")
print("\n数据集前5行:")
print(data_combined.head())
print("\n数据类型:")
print(data_combined.dtypes)
print("\n缺失值统计:")
print(data_combined.isnull().sum())

# 处理分类变量 - 将字符串转换为数值
from sklearn.preprocessing import LabelEncoder

# 创建标签编码器字典
label_encoders = {}
categorical_columns = data_combined.select_dtypes(include=['object']).columns

print(f"\n分类变量列: {list(categorical_columns)}")

# 对每个分类变量进行编码
for col in categorical_columns:
    le = LabelEncoder()
    data_combined[col] = le.fit_transform(data_combined[col])
    label_encoders[col] = le
    print(f"{col}: {list(le.classes_)}")

# 确保所有数据都是数值型
data_combined = data_combined.astype(float)

# 保存为CSV文件
csv_path = 'data/breast_cancer_wisconsin.csv'
data_combined.to_csv(csv_path, index=False)

print(f"\n数据集已保存到: {csv_path}")
print(f"最终数据集包含 {data_combined.shape[0]} 个样本，{data_combined.shape[1]} 个特征（包含目标变量）")
print(f"最终数据集形状: {data_combined.shape}")
print("\n编码后的数据集前5行:")
print(data_combined.head())
