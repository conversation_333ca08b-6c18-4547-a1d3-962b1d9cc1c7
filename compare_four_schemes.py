# 对比四种方案的性能测试脚本
# coding=utf-8
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
import argparse
import numpy as np
import tensorflow.compat.v1 as tf
import pandas as pd
import time
tf.disable_v2_behavior()

# 导入四种方案
from data_loader import data_loader
from utils import rmse_loss

# 四种方案的导入
import gadrop_v1
import gadrop_v2
import gadrop_v3
import gadrop_v4

def single_run(gain_module, scheme_name, data_name, mr, seed, batch_size, hint_rate, alpha, iterations):
    """
    针对单个方案执行一次完整的 GAIN 训练和插补流程
    """
    print(f"\n=== 开始测试 {scheme_name} ===")
    start_time = time.time()
    
    # 1) 重置默认图, 保证每次都是全新图
    tf.reset_default_graph()

    # 2) 设置随机种子
    tf.set_random_seed(seed)
    np.random.seed(seed)

    # 3) 读数据
    ori_data_x, miss_data_x, data_m = data_loader(data_name, mr)
    print(f"数据集: {data_name}, 缺失率: {mr}, 数据形状: {ori_data_x.shape}")

    # 4) 定义 GAIN 参数
    gain_parameters = {
        'batch_size': batch_size,
        'hint_rate': hint_rate,
        'alpha': alpha,
        'iterations': iterations
    }

    # 5) 训练 GAIN 并得到插补结果
    try:
        imputed_data_x = gain_module.gain(miss_data_x, gain_parameters)
        
        # 6) 计算 RMSE
        rmse = rmse_loss(ori_data_x, imputed_data_x, data_m)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"{scheme_name} - RMSE: {rmse:.6f}, 耗时: {duration:.2f}秒")
        return rmse, duration
        
    except Exception as e:
        print(f"{scheme_name} - 训练失败: {str(e)}")
        return None, None

def main():
    # 使用您选中的参数
    seed = 33
    data_name = 'breast'  # 您可以改为其他数据集
    batch_size = 64
    hint_rate = 0.9
    alpha = 100
    iterations = 10000
    
    # 测试的缺失率
    miss_rates = [0.1, 0.3, 0.5]
    
    # 四种方案
    schemes = [
        (gadrop_v1, "方案1: 串联连接"),
        (gadrop_v2, "方案2: 完全级联"),
        (gadrop_v3, "方案3: 加权融合"),
        (gadrop_v4, "方案4: Cross-Attention")
    ]
    
    # 存储结果
    results = []
    
    print("=" * 80)
    print("四种注意力融合方案性能对比测试")
    print("=" * 80)
    print(f"数据集: {data_name}")
    print(f"参数设置: batch_size={batch_size}, hint_rate={hint_rate}, alpha={alpha}, iterations={iterations}")
    print(f"随机种子: {seed}")
    print("=" * 80)
    
    for mr in miss_rates:
        print(f"\n{'='*20} 缺失率: {mr} {'='*20}")
        
        for gain_module, scheme_name in schemes:
            rmse, duration = single_run(
                gain_module, scheme_name, data_name, mr, seed, 
                batch_size, hint_rate, alpha, iterations
            )
            
            results.append({
                'scheme': scheme_name,
                'miss_rate': mr,
                'rmse': rmse,
                'duration': duration
            })
    
    # 整理和显示结果
    print("\n" + "=" * 80)
    print("最终结果汇总")
    print("=" * 80)
    
    df = pd.DataFrame(results)
    
    # 按缺失率分组显示结果
    for mr in miss_rates:
        print(f"\n缺失率 {mr}:")
        mr_results = df[df['miss_rate'] == mr].copy()
        mr_results = mr_results.sort_values('rmse')
        
        for idx, row in mr_results.iterrows():
            if row['rmse'] is not None:
                print(f"  {row['scheme']}: RMSE = {row['rmse']:.6f}, 耗时 = {row['duration']:.2f}秒")
            else:
                print(f"  {row['scheme']}: 训练失败")
    
    # 计算平均性能
    print(f"\n平均性能排名:")
    avg_results = df.groupby('scheme').agg({
        'rmse': 'mean',
        'duration': 'mean'
    }).sort_values('rmse')
    
    for scheme, row in avg_results.iterrows():
        if not pd.isna(row['rmse']):
            print(f"  {scheme}: 平均RMSE = {row['rmse']:.6f}, 平均耗时 = {row['duration']:.2f}秒")
    
    # 保存详细结果到CSV
    df.to_csv('four_schemes_comparison_results.csv', index=False)
    print(f"\n详细结果已保存到: four_schemes_comparison_results.csv")

if __name__ == '__main__':
    main()
