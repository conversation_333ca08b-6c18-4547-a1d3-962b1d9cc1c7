# 删去Q56  0.3746
# coding=utf-8
#
# Licensed under the Apache License, Version 2.0 ...
#
# GAIN function with reduced network depth (ladagain_simplified).
# Only keeps the first two "attention blocks" from the original ladagain.py

from utils import binary_sampler, uniform_sampler, sample_batch_index
from utils import xavier_init
from utils import normalization, renormalization, rounding
from tqdm import tqdm
import numpy as np
import tensorflow.compat.v1 as tf
tf.disable_v2_behavior()


def gain(data_x, gain_parameters):
    """
    Impute missing values in data_x using a simplified layered-attention GAIN.

    Args:
      - data_x: original data with missing values
      - gain_parameters: GAIN network parameters:
          batch_size, hint_rate, alpha, iterations

    Returns:
      - imputed_data: imputed data
    """
    # Define mask matrix
    data_m = 1 - np.isnan(data_x)

    # System parameters
    batch_size = gain_parameters['batch_size']
    hint_rate = gain_parameters['hint_rate']
    alpha = gain_parameters['alpha']
    iterations = gain_parameters['iterations']

    # Other parameters
    no, dim = data_x.shape

    # Normalization
    norm_data, norm_parameters = normalization(data_x)
    norm_data_x = np.nan_to_num(norm_data, 0)

    # Input placeholders
    X = tf.placeholder(tf.float32, shape=[None, dim])
    M = tf.placeholder(tf.float32, shape=[None, dim])
    H = tf.placeholder(tf.float32, shape=[None, dim])
    # Dropout rate placeholder for training/inference
    dropout_rate = tf.placeholder(tf.float32, shape=[])

    # ---------------------------------------------------------------------
    #                   Discriminator variables
    # ---------------------------------------------------------------------
    h_dim = int(dim)
    D_W1 = tf.Variable(xavier_init([dim * 2, h_dim]))  # Data + Hint
    D_b1 = tf.Variable(tf.zeros(shape=[h_dim]))

    D_W2 = tf.Variable(xavier_init([h_dim, h_dim]))
    D_b2 = tf.Variable(tf.zeros(shape=[h_dim]))

    D_W3 = tf.Variable(xavier_init([h_dim, dim]))
    D_b3 = tf.Variable(tf.zeros(shape=[dim]))  # multi-variate outputs

    theta_D = [D_W1, D_W2, D_W3, D_b1, D_b2, D_b3]

    # ---------------------------------------------------------------------
    #                   Generator variables
    #   We only keep the first two attention blocks, removing the third.
    # ---------------------------------------------------------------------
    # 1) For Q1/K1/V1 and Q2/K2/V2
    G_W1 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W2 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W3 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W4 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W5 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W6 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W7 = tf.Variable(xavier_init([dim * 4, dim * 2]))
    G_W8 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_b8 = tf.Variable(tf.zeros(shape=[dim * 2]))
    G_b9 = tf.Variable(tf.zeros(shape=[dim * 2]))
    G_W9 = tf.Variable(xavier_init([dim * 2, dim * 2]))

    # 2) For Q3/K3/V3 and Q4/K4/V4
    G_W10 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W11 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W12 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W13 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W14 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W15 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W16 = tf.Variable(xavier_init([dim * 4, dim * 2]))

    # The global vectors for the first 2 blocks only
    G_new_1 = tf.Variable(xavier_init([dim * 2, 1]))
    G_new_2 = tf.Variable(xavier_init([dim * 2, 1]))
    G_new_3 = tf.Variable(xavier_init([dim * 2, 1]))
    G_new_4 = tf.Variable(xavier_init([dim * 2, 1]))

    # We keep final output layer (dim*2 -> dim)
    G_W22 = tf.Variable(xavier_init([dim * 2, dim]))
    G_b22 = tf.Variable(tf.zeros(shape=[dim]))

    # We remove the entire third block (Q5/Q6, G_W17..21, G_new_5..6, etc.)

    theta_G = [
        G_W1, G_W2, G_W3, G_W4, G_W5, G_W6, G_W7, G_W8, G_W9, G_b8, G_b9,
        G_W10, G_W11, G_W12, G_W13, G_W14, G_W15, G_W16,
        G_new_1, G_new_2, G_new_3, G_new_4,
        G_W22, G_b22
    ]

    # ---------------------------------------------------------------------
    #                   Generator definition
    # ---------------------------------------------------------------------
    def generator(x, m, dropout_rate_val):
        """
        Generator with two attention blocks and dropout (instead of three).
        """

        # Combine real data (x) with mask (m)
        # x, m each: [batch_size, dim]
        # => inputs shape: [batch_size, 2*dim]
        inputs = tf.concat(values=[x, m], axis=1)
        dk = tf.cast(2 * dim, tf.float32)

        # ------------------------- 1st Attention Block -------------------------
        Q1 = tf.matmul(inputs, G_W1)
        K1 = tf.matmul(inputs, G_W2)
        V1 = tf.matmul(inputs, G_W3)

        q_attn_1 = tf.matmul(Q1, G_new_1)  # shape [batch,1]
        attn_1 = tf.nn.softmax(q_attn_1 / tf.sqrt(dk), axis=0)
        # Global query approach
        global_q_1 = tf.reduce_sum(attn_1 * Q1, axis=0, keepdims=True)
        p_1 = global_q_1 * K1
        r_1 = p_1 * V1
        G_h1 = r_1

        Q2 = tf.matmul(inputs, G_W4)
        K2 = tf.matmul(inputs, G_W5)
        V2 = tf.matmul(inputs, G_W6)

        q_attn_2 = tf.matmul(Q2, G_new_2)
        attn_2 = tf.nn.softmax(q_attn_2 / tf.sqrt(dk), axis=0)
        global_q_2 = tf.reduce_sum(attn_2 * Q2, axis=0, keepdims=True)
        p_2 = global_q_2 * K2
        r_2 = p_2 * V2
        G_h2 = r_2

        # Combine
        G_h3 = tf.concat(values=[G_h1, G_h2], axis=1)
        G_h4 = tf.add(tf.matmul(G_h3, G_W7), inputs)

        mean, var = tf.nn.moments(G_h4, axes=[0])
        G_h5 = tf.nn.batch_normalization(G_h4, mean, var, None, None, 1e-6)
        # Add dropout after batch normalization
        G_h5_drop = tf.nn.dropout(G_h5, rate=dropout_rate_val)
        G_h6 = tf.matmul(tf.nn.relu(tf.matmul(G_h5_drop, G_W8) + G_b8), G_W9) + G_b9
        G_h7 = tf.add(G_h5, G_h6)

        mean, var = tf.nn.moments(G_h7, axes=[0])
        G_h8 = tf.nn.batch_normalization(G_h7, mean, var, None, None, 1e-6)
        # Add dropout after second batch normalization
        G_h8_drop = tf.nn.dropout(G_h8, rate=dropout_rate_val)

        # ------------------------- 2nd Attention Block -------------------------
        # Use G_h8_drop from previous block
        Q3 = tf.matmul(inputs, G_W10)
        Q4 = tf.matmul(inputs, G_W11)
        K3 = tf.matmul(inputs, G_W12)
        K4 = tf.matmul(inputs, G_W13)
        V3 = tf.matmul(inputs, G_W14)
        V4 = tf.matmul(inputs, G_W15)

        q_attn_3 = tf.matmul(Q3, G_new_3)
        attn_3 = tf.nn.softmax(q_attn_3 / tf.sqrt(dk), axis=0)
        global_q_3 = tf.reduce_sum(attn_3 * Q3, axis=0, keepdims=True)
        p_3 = global_q_3 * K3
        r_3 = p_3 * V3
        G_h9 = r_3

        q_attn_4 = tf.matmul(Q4, G_new_4)
        attn_4 = tf.nn.softmax(q_attn_4 / tf.sqrt(dk), axis=0)
        global_q_4 = tf.reduce_sum(attn_4 * Q4, axis=0, keepdims=True)
        p_4 = global_q_4 * K4
        r_4 = p_4 * V4
        G_h10 = r_4

        G_h11 = tf.concat([G_h9, G_h10], axis=1)
        G_h12 = tf.add(tf.matmul(G_h11, G_W16), inputs)

        mean, var = tf.nn.moments(G_h12, axes=[0])
        G_h13 = tf.nn.batch_normalization(G_h12, mean, var, None, None, 1e-6)
        # Add dropout before final output
        G_h13_drop = tf.nn.dropout(G_h13, rate=dropout_rate_val)

        # ------------------- Remove the 3rd Attention Block -------------------
        # Instead, directly map G_h13_drop -> final output
        G_h14 = tf.nn.relu(G_h13_drop)
        G_prob = tf.nn.sigmoid(tf.matmul(G_h14, G_W22) + G_b22)

        return G_prob

    # ---------------------------------------------------------------------
    #                   Discriminator definition
    # ---------------------------------------------------------------------
    def discriminator(x, h):
        """
        Discriminator tries to distinguish real vs. imputed components.
        """
        inputs = tf.concat(values=[x, h], axis=1)
        D_h1 = tf.nn.relu(tf.matmul(inputs, D_W1) + D_b1)
        D_h2 = tf.nn.relu(tf.matmul(D_h1, D_W2) + D_b2)
        D_logit = tf.matmul(D_h2, D_W3) + D_b3
        D_prob = tf.nn.sigmoid(D_logit)
        return D_prob

    # ----------------- GAIN structure -----------------
    G_sample = generator(X, M, dropout_rate)
    Hat_X = X * M + G_sample * (1 - M)
    D_prob = discriminator(Hat_X, H)

    # ----------------- Loss functions -----------------
    D_loss_temp = -tf.reduce_mean(
        M * tf.log(D_prob + 1e-8) + (1 - M) * tf.log(1. - D_prob + 1e-8)
    )
    G_loss_temp = -tf.reduce_mean((1 - M) * tf.log(D_prob + 1e-8))
    MSE_loss = tf.reduce_mean((M * X - M * G_sample) ** 2) / tf.reduce_mean(M)

    D_loss = D_loss_temp
    G_loss = G_loss_temp + alpha * MSE_loss

    # ----------------- Optimizers -----------------
    D_solver = tf.train.AdamOptimizer().minimize(D_loss, var_list=theta_D)
    G_solver = tf.train.AdamOptimizer().minimize(G_loss, var_list=theta_G)

    # ----------------- Training -----------------
    sess = tf.Session()
    sess.run(tf.global_variables_initializer())

    for it in tqdm(range(iterations)):
        batch_idx = sample_batch_index(no, batch_size)
        X_mb = norm_data_x[batch_idx, :]
        M_mb = data_m[batch_idx, :]
        Z_mb = uniform_sampler(0, 0.01, batch_size, dim)
        H_mb_temp = binary_sampler(hint_rate, batch_size, dim)
        H_mb = M_mb * H_mb_temp

        X_mb = M_mb * X_mb + (1 - M_mb) * Z_mb

        # Update discriminator (no dropout for discriminator)
        _, D_loss_curr = sess.run([D_solver, D_loss_temp],
                                  feed_dict={X: X_mb, M: M_mb, H: H_mb, dropout_rate: 0.0})
        # Update generator (with dropout during training)
        _, G_loss_curr, MSE_loss_curr = sess.run(
            [G_solver, G_loss_temp, MSE_loss],
            feed_dict={X: X_mb, M: M_mb, H: H_mb, dropout_rate: 0.3}
        )

    # ----------------- Imputation -----------------
    Z_mb = uniform_sampler(0, 0.01, no, dim)
    X_mb = norm_data_x
    M_mb = data_m
    X_mb = M_mb * X_mb + (1 - M_mb) * Z_mb

    # No dropout during inference
    imputed_data = sess.run(G_sample, feed_dict={X: X_mb, M: M_mb, dropout_rate: 0.0})
    imputed_data = data_m * norm_data_x + (1 - data_m) * imputed_data

    # Post-processing
    imputed_data = renormalization(imputed_data, norm_parameters)
    imputed_data = rounding(imputed_data, data_x)

    return imputed_data