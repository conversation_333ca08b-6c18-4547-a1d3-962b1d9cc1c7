# 使用trans.py的示例main文件
# coding=utf-8
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
import argparse
import numpy as np
import tensorflow.compat.v1 as tf

import os
import pandas as pd
tf.disable_v2_behavior()

# 导入trans.py中的gain函数
from data_loader import data_loader
from trans import gain  # 使用Transformer注意力版本
from utils import rmse_loss
import time

def single_run(data_name, mr, seed, batch_size, hint_rate, alpha, iterations):
    """
    针对单个缺失率 mr 执行一次完整的 Transformer GAIN 训练和插补流程
    """
    start_time = time.time()
    # 1) 重置默认图, 保证每次都是全新图
    tf.reset_default_graph()

    # 2) 设置随机种子
    tf.set_random_seed(seed)
    np.random.seed(seed)

    # 3) 读数据
    ori_data_x, miss_data_x, data_m = data_loader(data_name, mr)

    # 4) 定义 GAIN 参数
    gain_parameters = {
        'batch_size': batch_size,
        'hint_rate': hint_rate,
        'alpha': alpha,
        'iterations': iterations
    }

    # 5) 训练 Transformer GAIN 并得到插补结果
    imputed_data_x = gain(miss_data_x, gain_parameters)

    # 6) 计算 RMSE
    rmse = rmse_loss(ori_data_x, imputed_data_x, data_m)

    end_time = time.time()
    duration = end_time - start_time

    print(f"Transformer GAIN - 数据集: {data_name}, 缺失率: {mr}, RMSE: {rmse:.6f}, 耗时: {duration:.2f}秒")
    
    return rmse

def main():
    """
    主函数：测试Transformer注意力GAIN
    """
    print("=" * 80)
    print("Transformer 注意力 GAIN 测试")
    print("=" * 80)
    
    # 参数设置
    data_name = 'breast'  # 可以改为其他数据集
    miss_rates = [0.1, 0.3, 0.5]
    seed = 33
    batch_size = 64
    hint_rate = 0.9
    alpha = 100
    iterations = 5000  # 可以根据需要调整
    
    results = []
    
    for mr in miss_rates:
        print(f"\n{'='*20} 缺失率: {mr} {'='*20}")
        
        rmse = single_run(
            data_name=data_name,
            mr=mr,
            seed=seed,
            batch_size=batch_size,
            hint_rate=hint_rate,
            alpha=alpha,
            iterations=iterations
        )
        
        results.append({
            'miss_rate': mr,
            'rmse': rmse
        })
    
    # 显示结果汇总
    print(f"\n{'='*80}")
    print("Transformer GAIN 结果汇总")
    print(f"{'='*80}")
    print(f"数据集: {data_name}")
    print(f"参数: batch_size={batch_size}, hint_rate={hint_rate}, alpha={alpha}, iterations={iterations}")
    print("-" * 40)
    
    for result in results:
        print(f"缺失率 {result['miss_rate']}: RMSE = {result['rmse']:.6f}")
    
    # 计算平均RMSE
    avg_rmse = np.mean([r['rmse'] for r in results])
    print("-" * 40)
    print(f"平均 RMSE: {avg_rmse:.6f}")

if __name__ == '__main__':
    main()
