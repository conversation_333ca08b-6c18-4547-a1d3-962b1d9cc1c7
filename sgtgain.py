# coding=utf-8
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

'''GAIN function.
Date: 2020/02/28
Reference: <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, "GAIN: Missing Data
           Imputation using Generative Adversarial Nets," ICML, 2018.
Paper Link: http://proceedings.mlr.press/v80/yoon18a/yoon18a.pdf
Contact: <EMAIL>
'''

# Necessary packages
# import tensorflow as tf
##IF USING TF 2 use following import to still use TF < 2.0 Functionalities
import tensorflow.compat.v1 as tf

tf.disable_v2_behavior()

import numpy as np
from tqdm import tqdm

from utils import normalization, renormalization, rounding
from utils import xavier_init
from utils import binary_sampler, uniform_sampler, sample_batch_index


def gain(data_x, gain_parameters):
    '''Impute missing values in data_x

    Args:
      - data_x: original data with missing values
      - gain_parameters: GAIN network parameters:
        - batch_size: Batch size
        - hint_rate: Hint rate
        - alpha: Hyperparameter
        - iterations: Iterations

    Returns:
      - imputed_data: imputed data
    '''
    # Define mask matrix
    data_m = 1 - np.isnan(data_x)

    # System parameters
    batch_size = gain_parameters['batch_size']
    hint_rate = gain_parameters['hint_rate']
    alpha = gain_parameters['alpha']
    iterations = gain_parameters['iterations']

    # Other parameters
    no, dim = data_x.shape

    # Hidden state dimensions
    h_dim = int(dim)

    # Normalization
    norm_data, norm_parameters = normalization(data_x)
    norm_data_x = np.nan_to_num(norm_data, 0)

    ## GAIN architecture
    # Input placeholders
    # Data vector
    X = tf.placeholder(tf.float32, shape=[None, dim])
    # Mask vector
    M = tf.placeholder(tf.float32, shape=[None, dim])
    # Hint vector
    H = tf.placeholder(tf.float32, shape=[None, dim])

    # Discriminator variables
    D_W1 = tf.Variable(xavier_init([dim * 2, h_dim]))  # Data + Hint as inputs
    D_b1 = tf.Variable(tf.zeros(shape=[h_dim]))

    D_W2 = tf.Variable(xavier_init([h_dim, h_dim]))
    D_b2 = tf.Variable(tf.zeros(shape=[h_dim]))

    D_W3 = tf.Variable(xavier_init([h_dim, dim]))
    D_b3 = tf.Variable(tf.zeros(shape=[dim]))  # Multi-variate outputs

    theta_D = [D_W1, D_W2, D_W3, D_b1, D_b2, D_b3]

    # Generator variables
    # Data + Mask as inputs (Random noise is in missing components)
    # G_W1 = tf.Variable(xavier_init([dim * 2, h_dim]))
    # G_b1 = tf.Variable(tf.zeros(shape=[h_dim]))
    #
    # G_W2 = tf.Variable(xavier_init([h_dim, h_dim]))
    # G_b2 = tf.Variable(tf.zeros(shape=[h_dim]))
    #
    # G_W3 = tf.Variable(xavier_init([h_dim, dim]))
    # G_b3 = tf.Variable(tf.zeros(shape=[dim]))
    #
    # theta_G = [G_W1, G_W2, G_W3, G_b1, G_b2, G_b3]

    # Generator variables 0
    # Data + Mask as inputs (Random noise is in missing components)
    G_W1 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W2 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W3 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W4 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W5 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W6 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W7 = tf.Variable(xavier_init([dim * 4, dim * 2]))
    G_W8 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_b8 = tf.Variable(tf.zeros(shape=[dim * 2]))
    G_b9 = tf.Variable(tf.zeros(shape=[dim * 2]))
    G_W9 = tf.Variable(xavier_init([dim * 2, dim * 2]))

    G_W10 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W11 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W12 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W13 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W14 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W15 = tf.Variable(xavier_init([dim * 2, dim * 2]))
    G_W16 = tf.Variable(xavier_init([dim * 4, dim * 2]))
    # Simplified: Direct output layer after attention blocks
    G_W_output = tf.Variable(xavier_init([dim * 2, dim]))
    G_b_output = tf.Variable(tf.zeros(shape=[dim]))

    theta_G = [G_W1, G_W2, G_W3, G_W4, G_W5, G_W6, G_W7, G_W8, G_W9, G_b8, G_b9,
                G_W10, G_W11, G_W12, G_W13, G_W14, G_W15, G_W16, G_W_output, G_b_output]

    ## GAIN functions
    # Generator
    def generator(x, m):
        # # Concatenate Mask and Data
        # inputs = tf.concat(values=[x, m], axis=1)
        # G_h1 = tf.nn.relu(tf.matmul(inputs, G_W1) + G_b1)
        # G_h2 = tf.nn.relu(tf.matmul(G_h1, G_W2) + G_b2)
        # # MinMax normalized output
        # G_prob = tf.nn.sigmoid(tf.matmul(G_h2, G_W3) + G_b3)

        dk =np.float32(2 * dim)
        inputs = tf.concat(values=[x, m], axis=1)
        Q1 = tf.matmul(inputs, G_W1)
        Q2 = tf.matmul(inputs, G_W4)
        K1 = tf.matmul(inputs, G_W2)
        K2 = tf.matmul(inputs, G_W5)
        V1 = tf.matmul(inputs, G_W3)
        V2 = tf.matmul(inputs, G_W6)
        G_h1 = tf.matmul(tf.nn.softmax(tf.multiply(tf.matmul(Q1, tf.transpose(K1)), 1 / tf.sqrt(dk))), V1)
        G_h2 = tf.matmul(tf.nn.softmax(tf.multiply(tf.matmul(Q2, tf.transpose(K2)), 1 / tf.sqrt(dk))), V2)
        G_h3 = tf.concat(values=[G_h1, G_h2], axis=1)
        G_h4 = tf.add(tf.matmul(G_h3, G_W7), inputs)
        mean, var = tf.nn.moments(G_h4, axes=[0])
        G_h5 = tf.nn.batch_normalization(G_h4, mean, var, None, None, variance_epsilon=1e-6)
        G_h6 = tf.matmul(tf.nn.relu(tf.matmul(G_h5, G_W8) + G_b8), G_W9) + G_b9
        G_h7 = tf.add(G_h5, G_h6)
        mean, var = tf.nn.moments(G_h7, axes=[0])
        G_h8 = tf.nn.batch_normalization(G_h7, mean, var, None, None, variance_epsilon=1e-6)

        Q3 = tf.matmul(inputs, G_W10)
        Q4 = tf.matmul(inputs, G_W11)
        K3 = tf.matmul(inputs, G_W12)
        K4 = tf.matmul(inputs, G_W13)
        V3 = tf.matmul(inputs, G_W14)
        V4 = tf.matmul(inputs, G_W15)
        G_h9 = tf.matmul(tf.nn.softmax(tf.multiply(tf.matmul(Q3, tf.transpose(K3)), 1 / tf.sqrt(dk))), V3)
        G_h10 = tf.matmul(tf.nn.softmax(tf.multiply(tf.matmul(Q4, tf.transpose(K4)), 1 / tf.sqrt(dk))), V4)
        G_h11 = tf.concat(values=[G_h9, G_h10], axis=1)
        G_h12 = tf.add(tf.matmul(G_h11, G_W16), inputs)
        mean, var = tf.nn.moments(G_h12, axes=[0])
        G_h13 = tf.nn.batch_normalization(G_h12, mean, var, None, None, variance_epsilon=1e-6)
        # 简化：直接通过激活函数连接到输出层，移除FFN和额外的残差连接
        G_h_activated = tf.nn.relu(G_h13)
        G_prob = tf.nn.sigmoid(tf.matmul(G_h_activated, G_W_output) + G_b_output)

        return G_prob
    # Discriminator
    def discriminator(x, h):
        # Concatenate Data and Hint
        inputs = tf.concat(values=[x, h], axis=1)
        D_h1 = tf.nn.relu(tf.matmul(inputs, D_W1) + D_b1)
        D_h2 = tf.nn.relu(tf.matmul(D_h1, D_W2) + D_b2)
        D_logit = tf.matmul(D_h2, D_W3) + D_b3
        D_prob = tf.nn.sigmoid(D_logit)
        return D_prob

    ## GAIN structure
    # Generator
    G_sample = generator(X, M)

    # Combine with observed data
    Hat_X = X * M + G_sample * (1 - M)

    # Discriminator
    D_prob = discriminator(Hat_X, H)

    ## GAIN loss
    D_loss_temp = -tf.reduce_mean(M * tf.log(D_prob + 1e-8) \
                                  + (1 - M) * tf.log(1. - D_prob + 1e-8))

    G_loss_temp = -tf.reduce_mean((1 - M) * tf.log(D_prob + 1e-8))

    MSE_loss = \
        tf.reduce_mean((M * X - M * G_sample) ** 2) / tf.reduce_mean(M)

    D_loss = D_loss_temp
    G_loss = G_loss_temp + alpha * MSE_loss

    ## GAIN solver
    D_solver = tf.train.AdamOptimizer().minimize(D_loss, var_list=theta_D)
    G_solver = tf.train.AdamOptimizer().minimize(G_loss, var_list=theta_G)

    ## Iterations
    sess = tf.Session()
    sess.run(tf.global_variables_initializer())

    # Start Iterations
    for it in tqdm(range(iterations)):
        # Sample batch
        batch_idx = sample_batch_index(no, batch_size)
        X_mb = norm_data_x[batch_idx, :]
        M_mb = data_m[batch_idx, :]
        # Sample random vectors
        Z_mb = uniform_sampler(0, 0.01, batch_size, dim)
        # Sample hint vectors
        H_mb_temp = binary_sampler(hint_rate, batch_size, dim)
        H_mb = M_mb * H_mb_temp

        # Combine random vectors with observed vectors
        X_mb = M_mb * X_mb + (1 - M_mb) * Z_mb

        _, D_loss_curr = sess.run([D_solver, D_loss_temp],
                                  feed_dict={M: M_mb, X: X_mb, H: H_mb})
        _, G_loss_curr, MSE_loss_curr = \
            sess.run([G_solver, G_loss_temp, MSE_loss],
                     feed_dict={X: X_mb, M: M_mb, H: H_mb})

    ## Return imputed data
    Z_mb = uniform_sampler(0, 0.01, no, dim)
    M_mb = data_m
    X_mb = norm_data_x
    X_mb = M_mb * X_mb + (1 - M_mb) * Z_mb

    imputed_data = sess.run([G_sample], feed_dict={X: X_mb, M: M_mb})[0]

    imputed_data = data_m * norm_data_x + (1 - data_m) * imputed_data

    # Renormalization
    imputed_data = renormalization(imputed_data, norm_parameters)

    # Rounding
    imputed_data = rounding(imputed_data, data_x)

    return imputed_data
