# 测试trans.py是否能正常运行
# coding=utf-8
import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
import numpy as np
import tensorflow.compat.v1 as tf
import time
tf.disable_v2_behavior()

# 导入必要模块
from data_loader import data_loader
from trans import gain  # 使用trans.py中的gain函数
from utils import rmse_loss

def test_trans():
    """
    测试trans.py中的Transformer注意力GAIN
    """
    print("=" * 60)
    print("测试 Transformer 注意力 GAIN")
    print("=" * 60)
    
    # 设置参数
    seed = 33
    data_name = 'breast'
    miss_rate = 0.3
    batch_size = 64
    hint_rate = 0.9
    alpha = 100
    iterations = 1000  # 减少迭代次数用于快速测试
    
    start_time = time.time()
    
    # 重置图和设置种子
    tf.reset_default_graph()
    tf.set_random_seed(seed)
    np.random.seed(seed)
    
    # 加载数据
    print(f"加载数据集: {data_name}, 缺失率: {miss_rate}")
    ori_data_x, miss_data_x, data_m = data_loader(data_name, miss_rate)
    print(f"数据形状: {ori_data_x.shape}")
    
    # 定义参数
    gain_parameters = {
        'batch_size': batch_size,
        'hint_rate': hint_rate,
        'alpha': alpha,
        'iterations': iterations
    }
    
    print(f"开始训练 Transformer GAIN...")
    print(f"参数: batch_size={batch_size}, hint_rate={hint_rate}, alpha={alpha}, iterations={iterations}")
    
    try:
        # 训练并插补
        imputed_data_x = gain(miss_data_x, gain_parameters)
        
        # 计算RMSE
        rmse = rmse_loss(ori_data_x, imputed_data_x, data_m)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ 测试成功!")
        print(f"RMSE: {rmse:.6f}")
        print(f"训练时间: {duration:.2f}秒")
        print(f"插补数据形状: {imputed_data_x.shape}")
        
        return True, rmse, duration
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None, None

if __name__ == '__main__':
    success, rmse, duration = test_trans()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 trans.py 可以正常运行!")
        print("可以在 main_letter_spam.py 中使用")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("⚠️  trans.py 存在问题，需要修复")
        print("=" * 60)
